# docdb - datasource.tf
# ============================================

# Account ID
#-------------------------------------------
data "aws_caller_identity" "current" {}

locals {
  account_id = data.aws_caller_identity.current.account_id
}

output "account_id" {
  description = "Account ID"
  value       = data.aws_caller_identity.current.account_id
}

# Region
#-------------------------------------------
data "aws_region" "current" {}

locals {
  account_region = data.aws_region.current.name
}

output "account_region" {
  description = "Region"
  value       = data.aws_region.current.name
}

# VPC
#-------------------------------------------
data "aws_vpc" "main-vpc" {
  tags = {
    Name = "${var.account_name}-${local.account_region}-vpc"
  }
}

# data.aws_vpc.main-vpc.cidr_block

# Private Subnets
#-------------------------------------------
data "aws_subnets" "private_subnets" {
  filter {
    name   = "vpc-id"
    values = [data.aws_vpc.main-vpc.id]
  }

  filter {
    name   = "tag:Name"
    values = ["${var.account_name}-${local.account_region}-private-subnet-*"]
  }
}