# s3-website-with-cloudfront - cloudwatch.tf
# ============================================

# Alarm for 4xx HTTP Errors
#-------------------------------------------
resource "aws_cloudwatch_metric_alarm" "http_4xx_errors" {
  alarm_name          = "${aws_cloudfront_distribution.this.id}_HTTP4xxErrorsAlarm"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "HTTPCode_Backend_4XX"
  namespace           = "AWS/CloudFront"
  period              = 300
  statistic           = "Sum"
  threshold           = 1
  alarm_description   = "${aws_cloudfront_distribution.this.id}: Alarm triggered when 4xx HTTP errors occur"
}

# Alarm for 5xx HTTP Errors
#-------------------------------------------
resource "aws_cloudwatch_metric_alarm" "http_5xx_errors" {
  alarm_name          = "${aws_cloudfront_distribution.this.id}_HTTP5xxErrorsAlarm"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "HTTPCode_Backend_5XX"
  namespace           = "AWS/CloudFront"
  period              = 300
  statistic           = "Sum"
  threshold           = 1
  alarm_description   = "${aws_cloudfront_distribution.this.id}: Alarm triggered when 5xx HTTP errors occur"
}

# Alarm for Cache Hit Rate
#-------------------------------------------
resource "aws_cloudwatch_metric_alarm" "cache_hit_rate" {
  alarm_name          = "${aws_cloudfront_distribution.this.id}_CacheHitRateAlarm"
  comparison_operator = "LessThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "CacheHitRate"
  namespace           = "AWS/CloudFront"
  period              = 300
  statistic           = "Average"
  threshold           = 90 # Adjust the threshold as needed
}

# Create an SNS Topic for CloudFront Alerts
#-------------------------------------------
resource "aws_sns_topic" "this" {
  name = "CloudfrontAlertsFrom${aws_cloudfront_distribution.this.id}"
}

# Grant CloudWatch permission to publish to the SNS Topic
#-------------------------------------------
resource "aws_sns_topic_policy" "this" {
  arn = aws_sns_topic.this.arn

  policy = jsonencode({
    Version = "2012-10-17",
    Id      = "AllowCloudWatchToPublish",
    Statement = [
      {
        Action    = "sns:Publish",
        Effect    = "Allow",
        Principal = {
          Service = "cloudwatch.amazonaws.com"
        },
        Resource = aws_sns_topic.this.arn,
        Condition = {
          ArnLike = {
            "aws:SourceArn" = "arn:aws:cloudwatch:*:*:alarm:*"
          }
        }
      }
    ]
  })
}