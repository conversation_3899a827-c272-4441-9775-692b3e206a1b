<PERSON><PERSON><PERSON><PERSON> desenvolvido pelo time de Plataformas para o provisionamento de "AWS S3 Website com CloudFront".

Este módulo tem como objetivo criar um Cloud Front que apontará para uma página web armazenada em um S3 Bucket, seguindo os padrões do CPE.

Serão criados os seguintes recursos:

S3 Bucket para armazenamento da página web com o nome no padrão ContaAWS-S3-UnidadeDeNegócio-Aplicação-Ambiente-Sufixo

CloudFront Distribution que direcionará o tráfego para a página web do S3 Bucket

IAM Policy para permitir que o CloudFront acesse o S3 Bucket com o nome no padrão AccessToS3-Bucket_cf

IAM Policy com permissão Read-Only para o Dynatrace com o nome no padrão AccessToCloudFront-CloudFrontID_dyntrc

Confira a documentação oficial no Confluence.

Como utilizar?
Passo 1
Precisamos configurar o Terrafora para armazenar o estado dos recursos criados por ele.

Caso não exista um arquivo para este fim, crie o arquivo "tf_01_backend.tf" com o conteúdo abaixo:

Terraform

# backend.tf - Script de definicao do Backend

# ============================================

terraform {
backend "s3" {
encrypt = true
}
}
Passo 2
Precisamos armazenar as definições de variáveis que serão utilizadas pelo Terraform.

Caso não exista um arquivo para este fim, crie o arquivo "tf_02_variables.tf" com o conteúdo a seguir.

Caso exista, adicione o conteúdo abaixo no arquivo:

Terraform

# variable.tf - Script de definicao de Variaveis

# ============================================

# Data Source

#-------------------------------------------
variable "account_name" {
description = "Nome da conta. Certificar que nao existem espacos em branco entre as palavras."
type = string
}

variable "account_region" {
description = "Regiao onde os recursos serao alocados."
type = string
}

# S3 Website with CloudFront

#-------------------------------------------
variable "s3-website-with-cloudfront" {
type = map(object({
business_unity = string
application_name = string
environment_acronym = string
bucket_suffix = string
bucket_versioning = string
bucket_expire_days = number
allowed_ip_ranges = list(any)
default_ttl = number
min_ttl = number
max_ttl = number
allowed_locations = list(string)
acm_certificate_arn = string
rate_limit_limit = number
sql_injection_field = list(string)
xss_field = list(string)
}))
}
Passo 3
Precisamos configurar e informar o Terraform em qual região os recursos serão implementados.

Caso não exista um arquivo para este fim, crie o arquivo "tf_03_provider.tf" com o conteúdo abaixo:

Terraform

# provider.tf - Script de definicao do Provider

# ============================================

provider "aws" {
region = var.account_region
}
Passo 4
O script abaixo será responsável por criar o S3 Bucket e o CloudFront.

Crie um arquivo no padrão "tf\_##\_s3_website-with-cloudfront.tf" e adicione:

Terraform

# s3-website-with-cloudfront.tf - Script de chamada do modulo S3 website with CloudFront

# =========================================================================================

module "s3-website-with-cloudfront" {
source = "git::https://gitportprd.portoseguro.brasil/cpe/core-cloud/modules/cpe-s3-website-with-cloudfront.git?ref=v.0.0.9"
for_each = var.s3-website-with-cloudfront

account_name = var.account_name
account_region = var.account_region
business_unity = each.value.business_unity
application_name = each.value.application_name
environment_acronym = each.value.environment_acronym
bucket_suffix = each.value.bucket_suffix
bucket_versioning = each.value.bucket_versioning
bucket_expire_days = each.value.bucket_expire_days
allowed_ip_ranges = each.value.allowed_ip_ranges
default_ttl = each.value.default_ttl
min_ttl = each.value.min_ttl
max_ttl = each.value.max_ttl
allowed_locations = each.value.allowed_locations
acm_certificate_arn = each.value.acm_certificate_arn
rate_limit_limit = each.value.rate_limit_limit
sql_injection_field = each.value.sql_injection_field
xss_field = each.value.xss_field
}
Passo 5
Adicione uma pasta env com os arquivos "dev.tfvars", "hml.tfvars" e "prd.tfvars". Em cada um destes arquivos você irá informar os valores das variáveis que o módulo utiliza.

Segue um exemplo do conteúdo de um arquivo "tfvars":

Terraform

# .tfvars - Arquivo de definicao de Valores de Variaveis

# ======================================================

# Data Source

#-------------------------------------------
account_name = "teste-dev"
account_region = "ca-central-1"

# S3 Website with CloudFront

#-------------------------------------------
s3-website-with-cloudfront = {
teste = {
business_unity = "exemplo"
application_name = "appteste"
environment_acronym = "dev"
bucket_suffix = "teste"
bucket_versioning = "Enabled"
bucket_expire_days = 30
allowed_ip_ranges = ["10.0.0.0/8", "**********/12", "***********/16"]
default_ttl = 86400
min_ttl = 3600
max_ttl = ********
allowed_locations = ["BR"]
acm_certificate_arn = "arn:aws:acm:us-east-1:************:certificate/********-1234-1234-1234-************"
rate_limit_limit = 100
sql_injection_field = ["username"]
xss_field = ["username"]
}
}
Requisitos
Nome Versão
Terraform > 0.13.1
AWS > 3.63

Exportar para as Planilhas
Recursos
Nome Tipo
aws_cloudfront_distribution resource
aws_s3_bucket resource

Exportar para as Planilhas
Entradas do módulo
A tabela a seguir segue a ordem presente no código.
| Nome | Descrição | Tipo | Default | Obrigatório |
|---|---|---|---|---|
| account_name | Nome da conta. Certificar que não existem espaços em branco entre as palavras. | string | "null" | sim |
| account_region | Região onde os recursos serão alocados. | string | "null" | sim |
| business_unity | Nome da Unidade de Negócio. Este texto será utilizado na construção do nome do recurso. | string | "null" | sim |
| application_name | Nome da Aplicação. Este texto será utilizado na construção do nome do recurso. | string | "null" | sim |
| environment_acronym | Acrônimo do Ambiente. Este texto será utilizado na construção do nome do recurso. | string | "null" | sim |
| bucket_suffix | Sufixo do Bucket. Este texto será utilizado na construção do nome do recurso. | string | "null" | sim |
| bucket_versioning | Define se o bucket S3 terá o versionamento habilitado ou desabilitado. | string | "Enabled" | sim |
| bucket_expire_days | Quantidade de dias que será usada na política de expiração de arquivos do Bucket logs. | number | "30" | sim |
| allowed_ip_ranges | Lista de intervalos de IP permitidos que será usada na política de acesso do Bucket. | list(any) | "["10.0.0.0/8", "**********/12", "***********/16"]" | sim |
| default_ttl | Tempo de vida padrão (em segundos) para o cache de objetos no CloudFront. | number | "86400" | sim |
| min_ttl | Tempo de vida mínimo (em segundos) para o cache de objetos no CloudFront. | number | "3600" | sim |
| max_ttl | Tempo de vida máximo (em segundos) para o cache de objetos no CloudFront. | number | "********" | sim |
| allowed_locations | Lista de localizações geográficas permitidas. | list(string) | "["BR"]" | sim |
| acm_certificate_arn | ARN do certificado SSL do CloudFront. O certificado deve estar na região us-east-1. | string | "null" | sim |
| rate_limit_limit | Limite de taxa para a regra de Rate Limit. | number | "100" | sim |
| sql_injection_field | Campo para corresponder à regra de SQL Injection. | list(string) | "["all_query_arguments"]" | sim |
| xss_field | Campo para corresponder à regra de XSS. | list(string) | "["all_query_arguments"]" | sim |
