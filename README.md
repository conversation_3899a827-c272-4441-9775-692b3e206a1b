# Módulo AWS S3 Website com CloudFront

[![Terraform](https://img.shields.io/badge/Terraform-%3E%3D0.13.1-blue.svg)](https://www.terraform.io/)
[![AWS Provider](https://img.shields.io/badge/AWS%20Provider-%3E%3D3.63-orange.svg)](https://registry.terraform.io/providers/hashicorp/aws/latest)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)

> Módulo Terraform para provisionamento de websites estáticos na AWS utilizando S3 e CloudFront com recursos de segurança integrados.

## 📋 Índice

- [Visão Geral](#-visão-geral)
- [Arquitetura](#-arquitetura)
- [Recursos Criados](#-recursos-criados)
- [Pré-requisitos](#-pré-requisitos)
- [Instalação e Uso](#-instalação-e-uso)
- [Exemplos](#-exemplos)
- [Variáveis de Entrada](#-variáveis-de-entrada)
- [Saídas](#-saídas)
- [Segurança](#-segurança)
- [Monitoramento](#-monitoramento)
- [Solução de Problemas](#-solução-de-problemas)
- [Contribuição](#-contribuição)
- [Licença](#-licença)

## 🎯 Visão Geral

Este módulo Terraform foi desenvolvido pelo time de Plataformas para automatizar o provisionamento de websites estáticos na AWS, seguindo as melhores práticas de segurança e performance. O módulo cria uma infraestrutura completa com S3 para armazenamento, CloudFront para distribuição global, WAF para proteção e monitoramento integrado.

### Principais Características

- ✅ **Distribuição Global**: CloudFront para baixa latência mundial
- ✅ **Segurança Avançada**: WAF integrado com proteção contra SQL Injection, XSS e Rate Limiting
- ✅ **SSL/TLS**: Suporte completo a certificados SSL
- ✅ **Monitoramento**: CloudWatch Alarms integrados
- ✅ **Compliance**: Seguindo padrões CPE (Cloud Platform Engineering)
- ✅ **Versionamento**: Controle de versão de arquivos no S3
- ✅ **Geo-blocking**: Controle de acesso por localização geográfica

## 🏗️ Arquitetura

```mermaid
graph TB
    User[👤 Usuário] --> CF[☁️ CloudFront Distribution]
    CF --> WAF[🛡️ WAF v2]
    CF --> S3[🪣 S3 Bucket Website]

    WAF --> GEO[🌍 Geo Blocking]
    WAF --> RATE[⚡ Rate Limiting]
    WAF --> SQL[🔒 SQL Injection Protection]
    WAF --> XSS[🛡️ XSS Protection]

    S3 --> LOGS[📊 S3 Logs Bucket]
    CF --> CW[📈 CloudWatch Alarms]

    IAM[🔑 IAM Policies] --> CF
    IAM --> S3
    IAM --> DT[📊 Dynatrace Integration]
```

## 📦 Recursos Criados

Este módulo provisiona os seguintes recursos AWS:

### Armazenamento

- **S3 Bucket Website**: Armazenamento dos arquivos estáticos
- **S3 Bucket Logs**: Armazenamento dos logs do CloudFront
- **Configuração de Website**: index.html e error.html
- **Versionamento**: Controle de versões dos arquivos
- **Criptografia**: AES256 server-side encryption

### Distribuição e Cache

- **CloudFront Distribution**: CDN global para distribuição de conteúdo
- **Origin Access Control**: Acesso seguro ao S3
- **Cache Behaviors**: Configurações otimizadas de cache
- **SSL/TLS**: Certificados ACM integrados

### Segurança

- **WAF v2 Web ACLs**: Proteção contra ameaças web
  - Geo-blocking por país
  - Rate limiting por IP
  - Proteção SQL Injection
  - Proteção XSS (Cross-Site Scripting)
- **IAM Policies**: Permissões granulares
- **Bucket Policies**: Acesso controlado ao S3

### Monitoramento

- **CloudWatch Alarms**: Alertas para erros 4xx e 5xx
- **Métricas Customizadas**: Monitoramento de WAF
- **Integração Dynatrace**: Políticas para observabilidade

## 🔧 Pré-requisitos

Antes de utilizar este módulo, certifique-se de ter:

### Ferramentas Necessárias

- [Terraform](https://www.terraform.io/downloads.html) >= 0.13.1
- [AWS CLI](https://aws.amazon.com/cli/) configurado
- Credenciais AWS com permissões adequadas

### Recursos AWS Existentes

- **Certificado ACM**: Deve estar na região `us-east-1` para CloudFront
- **VPC**: Rede virtual configurada seguindo padrões CPE
- **Subnets**: Sub-redes privadas configuradas

### Permissões IAM Necessárias

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:*",
        "cloudfront:*",
        "wafv2:*",
        "iam:*",
        "cloudwatch:*",
        "acm:ListCertificates"
      ],
      "Resource": "*"
    }
  ]
}
```

## 🚀 Instalação e Uso

### Passo 1: Configuração do Backend

Crie o arquivo `tf_01_backend.tf` para armazenar o estado do Terraform:

```hcl
# backend.tf - Configuração do Backend Terraform
# ============================================

terraform {
  backend "s3" {
    bucket         = "seu-bucket-terraform-state"
    key            = "s3-website-cloudfront/terraform.tfstate"
    region         = "us-east-1"
    encrypt        = true
    dynamodb_table = "terraform-state-lock"
  }
}
```

### Passo 2: Definição de Variáveis

Crie o arquivo `tf_02_variables.tf`:

```hcl
# variables.tf - Definição de Variáveis
# ====================================

variable "account_name" {
  description = "Nome da conta AWS (sem espaços)"
  type        = string
}

variable "account_region" {
  description = "Região onde os recursos serão criados"
  type        = string
}

variable "s3-website-with-cloudfront" {
  description = "Configuração do módulo S3 Website com CloudFront"
  type = map(object({
    business_unity       = string
    application_name     = string
    environment_acronym  = string
    bucket_suffix        = string
    bucket_versioning    = string
    bucket_expire_days   = number
    allowed_ip_ranges    = list(string)
    default_ttl          = number
    min_ttl              = number
    max_ttl              = number
    allowed_locations    = list(string)
    acm_certificate_arn  = string
    rate_limit_limit     = number
    sql_injection_field  = list(string)
    xss_field           = list(string)
  }))
}
```

### Passo 3: Configuração do Provider

Crie o arquivo `tf_03_provider.tf`:

```hcl
# provider.tf - Configuração do Provider AWS
# ==========================================

terraform {
  required_version = ">= 0.13.1"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = ">= 3.63"
    }
  }
}

provider "aws" {
  region = var.account_region
}

# Provider adicional para certificados ACM (us-east-1)
provider "aws" {
  alias  = "us_east_1"
  region = "us-east-1"
}
```

### Passo 4: Chamada do Módulo

Crie o arquivo `tf_04_s3-website-with-cloudfront.tf`:

```hcl
# s3-website-with-cloudfront.tf - Chamada do Módulo
# =================================================

module "s3-website-with-cloudfront" {
  source   = "git::https://gitportprd.portoseguro.brasil/cpe/core-cloud/modules/cpe-s3-website-with-cloudfront.git?ref=v.0.0.9"
  for_each = var.s3-website-with-cloudfront

  # Configurações da Conta
  account_name   = var.account_name
  account_region = var.account_region

  # Configurações da Aplicação
  business_unity      = each.value.business_unity
  application_name    = each.value.application_name
  environment_acronym = each.value.environment_acronym
  bucket_suffix       = each.value.bucket_suffix

  # Configurações do S3
  bucket_versioning  = each.value.bucket_versioning
  bucket_expire_days = each.value.bucket_expire_days
  allowed_ip_ranges  = each.value.allowed_ip_ranges

  # Configurações do CloudFront
  default_ttl         = each.value.default_ttl
  min_ttl            = each.value.min_ttl
  max_ttl            = each.value.max_ttl
  allowed_locations  = each.value.allowed_locations
  acm_certificate_arn = each.value.acm_certificate_arn

  # Configurações do WAF
  rate_limit_limit     = each.value.rate_limit_limit
  sql_injection_field  = each.value.sql_injection_field
  xss_field           = each.value.xss_field
}
```

### Passo 5: Configuração de Ambientes

Crie a estrutura de diretórios para diferentes ambientes:

```
env/
├── dev.tfvars
├── hml.tfvars
└── prd.tfvars
```

## 📝 Exemplos

### Exemplo Básico - Desenvolvimento

Arquivo `env/dev.tfvars`:

```hcl
# Configurações de Desenvolvimento
# ================================

account_name   = "minha-conta-dev"
account_region = "us-east-1"

s3-website-with-cloudfront = {
  meu-site = {
    business_unity      = "tecnologia"
    application_name    = "portal-dev"
    environment_acronym = "dev"
    bucket_suffix       = "v1"

    # Configurações S3
    bucket_versioning  = "Enabled"
    bucket_expire_days = 30
    allowed_ip_ranges  = [
      "10.0.0.0/8",
      "**********/12",
      "***********/16"
    ]

    # Configurações CloudFront
    default_ttl = 86400    # 1 dia
    min_ttl     = 3600     # 1 hora
    max_ttl     = ******** # 1 ano

    # Configurações de Segurança
    allowed_locations       = ["BR", "US"]
    acm_certificate_arn     = "arn:aws:acm:us-east-1:************:certificate/********-1234-1234-1234-************"
    rate_limit_limit        = 1000
    sql_injection_field     = ["all_query_arguments", "uri_path"]
    xss_field              = ["all_query_arguments", "body"]
  }
}
```

### Exemplo Avançado - Produção

Arquivo `env/prd.tfvars`:

```hcl
# Configurações de Produção
# =========================

account_name   = "minha-conta-prd"
account_region = "us-east-1"

s3-website-with-cloudfront = {
  portal-principal = {
    business_unity      = "vendas"
    application_name    = "portal-cliente"
    environment_acronym = "prd"
    bucket_suffix       = "v2"

    # Configurações S3 - Produção
    bucket_versioning  = "Enabled"
    bucket_expire_days = 90
    allowed_ip_ranges  = [
      "10.0.0.0/8",
      "**********/12"
    ]

    # Configurações CloudFront - Otimizadas
    default_ttl = 604800   # 7 dias
    min_ttl     = 86400    # 1 dia
    max_ttl     = ******** # 1 ano

    # Configurações de Segurança - Restritivas
    allowed_locations       = ["BR"]
    acm_certificate_arn     = "arn:aws:acm:us-east-1:************:certificate/********-4321-4321-4321-************"
    rate_limit_limit        = 500
    sql_injection_field     = ["all_query_arguments", "uri_path", "body"]
    xss_field              = ["all_query_arguments", "uri_path", "body"]
  }

  api-docs = {
    business_unity      = "tecnologia"
    application_name    = "documentacao"
    environment_acronym = "prd"
    bucket_suffix       = "docs"

    bucket_versioning  = "Enabled"
    bucket_expire_days = 365
    allowed_ip_ranges  = ["0.0.0.0/0"] # Público

    default_ttl = 3600     # 1 hora
    min_ttl     = 1800     # 30 minutos
    max_ttl     = 86400    # 1 dia

    allowed_locations       = ["BR", "US", "CA"]
    acm_certificate_arn     = "arn:aws:acm:us-east-1:************:certificate/********-4321-4321-4321-************"
    rate_limit_limit        = 2000
    sql_injection_field     = ["all_query_arguments"]
    xss_field              = ["all_query_arguments"]
  }
}
```

### Comandos de Execução

```bash
# Inicializar o Terraform
terraform init

# Planejar as mudanças (desenvolvimento)
terraform plan -var-file="env/dev.tfvars"

# Aplicar as mudanças (desenvolvimento)
terraform apply -var-file="env/dev.tfvars"

# Aplicar em produção
terraform apply -var-file="env/prd.tfvars"

# Destruir recursos (cuidado!)
terraform destroy -var-file="env/dev.tfvars"
```

## 📊 Variáveis de Entrada

### Variáveis Obrigatórias

| Nome                  | Descrição                                                      | Tipo     | Exemplo                                                |
| --------------------- | -------------------------------------------------------------- | -------- | ------------------------------------------------------ |
| `account_name`        | Nome da conta AWS (sem espaços em branco)                      | `string` | `"minha-conta-dev"`                                    |
| `account_region`      | Região onde os recursos serão alocados                         | `string` | `"us-east-1"`                                          |
| `business_unity`      | Nome da Unidade de Negócio para construção do nome do recurso  | `string` | `"tecnologia"`                                         |
| `application_name`    | Nome da Aplicação para construção do nome do recurso           | `string` | `"portal-cliente"`                                     |
| `environment_acronym` | Acrônimo do Ambiente para construção do nome do recurso        | `string` | `"dev"`                                                |
| `bucket_suffix`       | Sufixo do Bucket para construção do nome do recurso            | `string` | `"v1"`                                                 |
| `acm_certificate_arn` | ARN do certificado SSL do CloudFront (deve estar em us-east-1) | `string` | `"arn:aws:acm:us-east-1:************:certificate/..."` |

### Variáveis Opcionais

| Nome                  | Descrição                                                | Tipo           | Padrão                                              | Exemplo                               |
| --------------------- | -------------------------------------------------------- | -------------- | --------------------------------------------------- | ------------------------------------- |
| `bucket_versioning`   | Define se o bucket S3 terá versionamento habilitado      | `string`       | `"Enabled"`                                         | `"Enabled"` ou `"Disabled"`           |
| `bucket_expire_days`  | Dias para expiração de arquivos do bucket de logs        | `number`       | `30`                                                | `90`                                  |
| `allowed_ip_ranges`   | Lista de ranges IP permitidos para acesso ao bucket      | `list(string)` | `["10.0.0.0/8", "**********/12", "***********/16"]` | `["0.0.0.0/0"]`                       |
| `default_ttl`         | Tempo de vida padrão (segundos) para cache do CloudFront | `number`       | `86400`                                             | `604800`                              |
| `min_ttl`             | Tempo de vida mínimo (segundos) para cache do CloudFront | `number`       | `3600`                                              | `1800`                                |
| `max_ttl`             | Tempo de vida máximo (segundos) para cache do CloudFront | `number`       | `********`                                          | `86400`                               |
| `allowed_locations`   | Lista de códigos de países permitidos (geo-blocking)     | `list(string)` | `["BR"]`                                            | `["BR", "US", "CA"]`                  |
| `rate_limit_limit`    | Limite de taxa para regra de Rate Limiting do WAF        | `number`       | `100`                                               | `1000`                                |
| `sql_injection_field` | Campos para verificação de SQL Injection                 | `list(string)` | `["all_query_arguments"]`                           | `["all_query_arguments", "uri_path"]` |
| `xss_field`           | Campos para verificação de XSS                           | `list(string)` | `["all_query_arguments"]`                           | `["all_query_arguments", "body"]`     |

### Convenções de Nomenclatura

Os recursos seguem o padrão de nomenclatura CPE:

```
{account_id}-{service}-{business_unity}-{application_name}-{environment_acronym}-{suffix}
```

**Exemplo:**

- Bucket S3: `************-s3-tecnologia-portal-dev-v1`
- CloudFront: `E********90ABC`
- IAM Policy: `AccessToS3-************-s3-tecnologia-portal-dev-v1_cf`

## 📤 Saídas

### Saídas do S3

| Nome                    | Descrição                                       | Tipo          |
| ----------------------- | ----------------------------------------------- | ------------- |
| `website_bucket_domain` | Nome de domínio regional do bucket website      | `map(string)` |
| `website_bucket_arn`    | ARN do bucket website                           | `map(string)` |
| `website_bucket_name`   | Nome do bucket website                          | `map(string)` |
| `website_bucket_id`     | ID do bucket website                            | `map(string)` |
| `cflogs_bucket_domain`  | Nome de domínio do bucket de logs do CloudFront | `string`      |
| `cflogs_bucket_arn`     | ARN do bucket de logs do CloudFront             | `string`      |
| `cflogs_bucket_name`    | Nome do bucket de logs do CloudFront            | `string`      |
| `cflogs_bucket_id`      | ID do bucket de logs do CloudFront              | `string`      |

### Saídas do CloudFront

| Nome                             | Descrição                          | Tipo     |
| -------------------------------- | ---------------------------------- | -------- |
| `cloudfront_distribution_id`     | ID da distribuição CloudFront      | `string` |
| `cloudfront_distribution_domain` | Domínio da distribuição CloudFront | `string` |

### Saídas das Políticas IAM

| Nome         | Descrição                             | Tipo          |
| ------------ | ------------------------------------- | ------------- |
| `policy_map` | Mapa com ARNs das políticas de acesso | `map(string)` |

**Estrutura do `policy_map`:**

```hcl
{
  "s3-cloudfront-website" = {
    "app1" = "arn:aws:iam::************:policy/AccessToS3-bucket_cf"
    "app2" = "arn:aws:iam::************:policy/AccessToS3-bucket_cf"
  }
  "s3-cloudfront-cflogs" = "arn:aws:iam::************:policy/AccessToS3-logs_cf"
  "waf-cloudfront"       = "arn:aws:iam::************:policy/AccessToWAF-cloudfront_cf"
  "cloudfront-dynatrace" = "arn:aws:iam::************:policy/AccessToCloudFront-ID_dyntrc"
}
```

### Saídas do WAF

| Nome          | Descrição                         | Tipo          |
| ------------- | --------------------------------- | ------------- |
| `waf_acl_map` | Mapa com ARNs das Web ACLs do WAF | `map(string)` |

**Estrutura do `waf_acl_map`:**

```hcl
{
  "geoblocking"   = "arn:aws:wafv2:us-east-1:************:global/webacl/geoblocking/..."
  "rate_limit"    = "arn:aws:wafv2:us-east-1:************:global/webacl/rate_limit/..."
  "sql_injection" = "arn:aws:wafv2:us-east-1:************:global/webacl/sql_injection/..."
  "xss"           = "arn:aws:wafv2:us-east-1:************:global/webacl/xss/..."
  "all_in_one"    = "arn:aws:wafv2:us-east-1:************:global/webacl/all_in_one/..."
}
```

### Exemplo de Uso das Saídas

```hcl
# Referenciando saídas do módulo
output "website_url" {
  description = "URL do website"
  value       = "https://${module.s3-website-with-cloudfront["meu-site"].cloudfront_distribution_domain}"
}

output "bucket_name" {
  description = "Nome do bucket criado"
  value       = module.s3-website-with-cloudfront["meu-site"].website_bucket_name["portal-dev"]
}
```

## 🔒 Segurança

### Recursos de Segurança Implementados

#### WAF (Web Application Firewall)

- **Geo-blocking**: Restringe acesso por localização geográfica
- **Rate Limiting**: Previne ataques de força bruta e DDoS
- **SQL Injection Protection**: Detecta e bloqueia tentativas de SQL injection
- **XSS Protection**: Protege contra ataques de Cross-Site Scripting

#### S3 Security

- **Bucket Policies**: Acesso restrito baseado em IP ranges
- **Server-Side Encryption**: Criptografia AES256 automática
- **Versioning**: Controle de versões para recuperação de dados
- **Access Logging**: Logs detalhados de acesso

#### CloudFront Security

- **Origin Access Control (OAC)**: Acesso seguro ao S3
- **SSL/TLS**: Certificados ACM com TLS 1.2+
- **Security Headers**: Headers de segurança automáticos
- **Geo Restrictions**: Controle de acesso por país

#### IAM Security

- **Least Privilege**: Políticas com permissões mínimas necessárias
- **Resource-Based Policies**: Controle granular de acesso
- **Service-Linked Roles**: Integração segura entre serviços

### Melhores Práticas de Segurança

1. **Certificados SSL**

   - Use sempre certificados ACM válidos
   - Mantenha certificados atualizados
   - Configure redirecionamento HTTP → HTTPS

2. **Configuração de WAF**

   - Monitore métricas do WAF regularmente
   - Ajuste rate limits conforme necessário
   - Mantenha regras de proteção atualizadas

3. **Acesso ao S3**

   - Restrinja IP ranges ao mínimo necessário
   - Use versionamento para backup automático
   - Configure lifecycle policies para logs

4. **Monitoramento**
   - Configure alertas CloudWatch
   - Monitore logs de acesso regularmente
   - Use AWS Config para compliance

### Configurações de Segurança Recomendadas

```hcl
# Exemplo de configuração segura para produção
s3-website-with-cloudfront = {
  secure-site = {
    # Configurações restritivas
    allowed_ip_ranges = [
      "10.0.0.0/8",      # Rede corporativa
      "**********/12"    # VPN
    ]

    # Geo-blocking restritivo
    allowed_locations = ["BR"]

    # Rate limiting agressivo
    rate_limit_limit = 100

    # Proteção abrangente
    sql_injection_field = [
      "all_query_arguments",
      "uri_path",
      "body"
    ]

    xss_field = [
      "all_query_arguments",
      "uri_path",
      "body"
    ]

    # Logs com retenção longa
    bucket_expire_days = 365
  }
}
```

## 📈 Monitoramento

### CloudWatch Alarms Incluídos

O módulo cria automaticamente os seguintes alarmes:

#### Alarmes de Erro HTTP

- **4xx Errors**: Detecta erros de cliente (404, 403, etc.)
- **5xx Errors**: Detecta erros de servidor
- **Threshold**: 1 erro em 5 minutos
- **Ação**: Notificação via SNS (configurar separadamente)

#### Métricas do WAF

- **Blocked Requests**: Requisições bloqueadas por regra
- **Rate Limit Hits**: Ativações do rate limiting
- **Geo Blocking**: Bloqueios por localização
- **SQL/XSS Detection**: Tentativas de ataque detectadas

### Métricas Personalizadas

```hcl
# Exemplo de configuração de alarme personalizado
resource "aws_cloudwatch_metric_alarm" "high_error_rate" {
  alarm_name          = "website-high-error-rate"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "HTTPCode_Backend_4XX"
  namespace           = "AWS/CloudFront"
  period              = "300"
  statistic           = "Sum"
  threshold           = "10"
  alarm_description   = "High 4xx error rate detected"

  dimensions = {
    DistributionId = module.s3-website-with-cloudfront["site"].cloudfront_distribution_id
  }
}
```

### Dashboard CloudWatch

Crie um dashboard para monitoramento centralizado:

```hcl
resource "aws_cloudwatch_dashboard" "website_dashboard" {
  dashboard_name = "S3-CloudFront-Website-Dashboard"

  dashboard_body = jsonencode({
    widgets = [
      {
        type   = "metric"
        width  = 12
        height = 6
        properties = {
          metrics = [
            ["AWS/CloudFront", "Requests", "DistributionId", module.s3-website-with-cloudfront["site"].cloudfront_distribution_id],
            [".", "BytesDownloaded", ".", "."],
            [".", "HTTPCode_Backend_4XX", ".", "."],
            [".", "HTTPCode_Backend_5XX", ".", "."]
          ]
          period = 300
          stat   = "Sum"
          region = "us-east-1"
          title  = "CloudFront Metrics"
        }
      }
    ]
  })
}
```

### Integração com Dynatrace

O módulo inclui políticas IAM para integração com Dynatrace:

- **Read-Only Access**: Acesso de leitura ao CloudFront
- **Metrics Collection**: Coleta de métricas personalizadas
- **Log Analysis**: Análise de logs de acesso

## 🔧 Solução de Problemas

### Problemas Comuns

#### 1. Erro de Certificado SSL

**Sintoma**: CloudFront não aceita o certificado ACM

```
Error: InvalidViewerCertificate: The certificate must be in us-east-1
```

**Solução**:

```bash
# Verificar região do certificado
aws acm list-certificates --region us-east-1

# Criar certificado na região correta
aws acm request-certificate \
  --domain-name exemplo.com \
  --validation-method DNS \
  --region us-east-1
```

#### 2. Bucket S3 já existe

**Sintoma**: Erro ao criar bucket S3

```
Error: BucketAlreadyExists: The requested bucket name is not available
```

**Solução**:

- Altere o `bucket_suffix` para um valor único
- Verifique se o bucket não foi criado em outra conta
- Use nomes mais específicos incluindo timestamp

#### 3. WAF não está bloqueando

**Sintoma**: Requisições maliciosas passando pelo WAF

**Diagnóstico**:

```bash
# Verificar métricas do WAF
aws cloudwatch get-metric-statistics \
  --namespace AWS/WAFV2 \
  --metric-name BlockedRequests \
  --start-time 2024-01-01T00:00:00Z \
  --end-time 2024-01-01T23:59:59Z \
  --period 3600 \
  --statistics Sum
```

**Solução**:

- Verificar se o WAF está associado ao CloudFront
- Revisar regras do WAF
- Ajustar campos de verificação (sql_injection_field, xss_field)

#### 4. Cache não está funcionando

**Sintoma**: Baixa performance, muitas requisições ao S3

**Diagnóstico**:

```bash
# Verificar hit rate do cache
aws cloudwatch get-metric-statistics \
  --namespace AWS/CloudFront \
  --metric-name CacheHitRate \
  --dimensions Name=DistributionId,Value=E********90ABC \
  --start-time 2024-01-01T00:00:00Z \
  --end-time 2024-01-01T23:59:59Z \
  --period 3600 \
  --statistics Average
```

**Solução**:

- Ajustar valores de TTL (min_ttl, default_ttl, max_ttl)
- Verificar headers Cache-Control dos arquivos
- Configurar behaviors específicos para diferentes tipos de arquivo

### Comandos Úteis para Diagnóstico

```bash
# Verificar status da distribuição CloudFront
aws cloudfront get-distribution --id E********90ABC

# Listar certificados ACM
aws acm list-certificates --region us-east-1

# Verificar logs do S3
aws s3 ls s3://bucket-logs/ --recursive

# Testar conectividade
curl -I https://d********90abc.cloudfront.net

# Verificar regras do WAF
aws wafv2 get-web-acl --scope CLOUDFRONT --id ********-1234-1234-1234-************
```

## 🤝 Contribuição

### Como Contribuir

1. **Fork** o repositório
2. **Clone** sua fork localmente
3. **Crie** uma branch para sua feature (`git checkout -b feature/nova-funcionalidade`)
4. **Commit** suas mudanças (`git commit -am 'Adiciona nova funcionalidade'`)
5. **Push** para a branch (`git push origin feature/nova-funcionalidade`)
6. **Abra** um Pull Request

### Padrões de Desenvolvimento

#### Estrutura de Commits

```
tipo(escopo): descrição breve

Descrição mais detalhada do que foi alterado e por quê.

Fixes #123
```

**Tipos de commit:**

- `feat`: Nova funcionalidade
- `fix`: Correção de bug
- `docs`: Documentação
- `style`: Formatação
- `refactor`: Refatoração
- `test`: Testes
- `chore`: Tarefas de manutenção

#### Testes

Antes de submeter um PR, execute:

```bash
# Validação do Terraform
terraform fmt -check
terraform validate

# Testes de segurança
tfsec .

# Linting
tflint

# Testes de integração
terratest
```

#### Documentação

- Mantenha o README.md atualizado
- Documente todas as variáveis
- Inclua exemplos práticos
- Atualize o CHANGELOG.md

### Reportando Issues

Ao reportar problemas, inclua:

1. **Versão** do Terraform e providers
2. **Configuração** utilizada (sem dados sensíveis)
3. **Logs de erro** completos
4. **Passos** para reproduzir o problema
5. **Comportamento esperado** vs **comportamento atual**

### Roadmap

#### Próximas Funcionalidades

- [ ] Suporte a múltiplos domínios
- [ ] Integração com AWS Config
- [ ] Backup automático do S3
- [ ] Suporte a Lambda@Edge
- [ ] Métricas customizadas do WAF
- [ ] Integração com AWS Security Hub

#### Melhorias Planejadas

- [ ] Otimização de performance
- [ ] Redução de custos
- [ ] Automação de testes
- [ ] Documentação interativa
- [ ] Templates de exemplo

## 📋 Requisitos do Sistema

### Versões Suportadas

| Ferramenta   | Versão Mínima | Versão Recomendada | Notas                           |
| ------------ | ------------- | ------------------ | ------------------------------- |
| Terraform    | 0.13.1        | 1.5+               | Suporte a `for_each` necessário |
| AWS Provider | 3.63          | 5.0+               | Recursos WAFv2 e CloudFront OAC |
| AWS CLI      | 2.0           | 2.13+              | Para comandos de diagnóstico    |

### Permissões AWS Necessárias

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:CreateBucket",
        "s3:DeleteBucket",
        "s3:GetBucketPolicy",
        "s3:PutBucketPolicy",
        "s3:PutBucketVersioning",
        "s3:PutEncryptionConfiguration",
        "s3:PutBucketWebsite",
        "cloudfront:CreateDistribution",
        "cloudfront:UpdateDistribution",
        "cloudfront:DeleteDistribution",
        "cloudfront:GetDistribution",
        "cloudfront:CreateOriginAccessControl",
        "wafv2:CreateWebACL",
        "wafv2:UpdateWebACL",
        "wafv2:DeleteWebACL",
        "wafv2:GetWebACL",
        "wafv2:CreateRegexPatternSet",
        "iam:CreatePolicy",
        "iam:DeletePolicy",
        "iam:GetPolicy",
        "cloudwatch:PutMetricAlarm",
        "cloudwatch:DeleteAlarms"
      ],
      "Resource": "*"
    }
  ]
}
```

### Limites e Quotas AWS

Verifique os seguintes limites em sua conta:

- **CloudFront Distributions**: 200 por conta (padrão)
- **S3 Buckets**: 100 por conta (padrão)
- **WAF Web ACLs**: 100 por região (padrão)
- **IAM Policies**: 1500 por conta (padrão)

## 📄 Licença

Este projeto está licenciado sob a Licença MIT - veja o arquivo [LICENSE](LICENSE) para detalhes.

```
MIT License

Copyright (c) 2024 Time de Plataformas

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

## 📞 Suporte

### Canais de Suporte

- **Issues GitHub**: Para bugs e solicitações de funcionalidades
- **Discussions**: Para perguntas e discussões gerais
- **Wiki**: Documentação adicional e tutoriais
- **Confluence**: Documentação oficial da empresa

### Contatos

- **Time de Plataformas**: <EMAIL>
- **Slack**: #cloud-platform-engineering
- **Teams**: Canal CPE

### SLA de Suporte

- **Crítico**: 4 horas úteis
- **Alto**: 1 dia útil
- **Médio**: 3 dias úteis
- **Baixo**: 5 dias úteis

---

<div align="center">

**Desenvolvido com ❤️ pelo Time de Plataformas**

[🏠 Home](README.md) | [📚 Documentação](docs/) | [🐛 Issues](issues/) | [💬 Discussions](discussions/)

</div>
