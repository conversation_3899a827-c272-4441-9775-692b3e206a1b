# s3-website-with-cloudfront - waf.tf
# ============================================

# SQL Injection Rule - Componente de Regex
#-------------------------------------------
resource "aws_wafv2_regex_pattern_set" "this" {
  name        = "default-regex-${aws_cloudfront_distribution.this.id}"
  description = "Default regex pattern set"
  scope       = "REGIONAL"

  regular_expression {
    regex_string = ".[Ss]+[Ee]+[Ll]+[Ee]+[Cc]+[Tt]+."
  }
}

# All-in-One Rule (Web ACL Combinada)
#-------------------------------------------
resource "aws_wafv2_web_acl" "all_in_one" {
  name        = "CombinedWebACL-${aws_cloudfront_distribution.this.id}"
  description = "Web ACL that combines rules from multiple ACLs"
  scope       = "REGIONAL"

  default_action {
    allow {}
  }

  visibility_config {
    cloudwatch_metrics_enabled = true
    metric_name                = "CombinedAclMetrics"
    sampled_requests_enabled   = true
  }

  # Regra GeoBlocking
  rule {
    name     = "GeoBlockingRule"
    priority = 1

    action {
      allow {}
    }

    statement {
      geo_match_statement {
        country_codes = var.allowed_locations
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "GeoBlockingMetrics" # Corrigido para ser mais específico
      sampled_requests_enabled   = true
    }
  }

  # Regra Rate limiting
  rule {
    name     = "RateLimitRule"
    priority = 2

    action {
      block {}
    }

    statement {
      rate_based_statement {
        limit              = var.rate_limit_limit
        aggregate_key_type = "IP"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "RateLimitMetrics"
      sampled_requests_enabled   = true
    }
  }

  # Regra SQL Injection
  rule {
    name     = "SQLInjectionRule"
    priority = 3

    action {
      block {}
    }

    dynamic "statement" {
      for_each = var.sql_injection_field
      content {
        regex_pattern_set_reference_statement {
          arn = aws_wafv2_regex_pattern_set.this.arn
          field_to_match {
            single_header {
              name = statement.value
            }
          }
          text_transformation {
            priority = 0
            type     = "NONE"
          }
        }
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "SQLInjectionMetrics"
      sampled_requests_enabled   = true
    }
  }

  # Regra XSS
  rule {
    name     = "XSSRule"
    priority = 4

    action {
      block {}
    }

    dynamic "statement" {
      for_each = var.xss_field
      content {
        xss_match_statement {
          field_to_match {
            single_header {
              name = statement.value
            }
          }
          text_transformation {
            priority = 0
            type     = "NONE"
          }
        }
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "XSSMetrics"
      sampled_requests_enabled   = true
    }
  }
}

# All-in-One Rule - Association with Cloudfront
#-------------------------------------------
# resource "aws_wafv2_web_acl_association" "this" {
#   resource_arn = aws_cloudfront_distribution.this.arn
#   web_acl_arn  = aws_wafv2_web_acl.all_in_one.arn
# }
#
# depends_on = [aws_cloudfront_distribution.this, aws_wafv2_web_acl.all_in_one]