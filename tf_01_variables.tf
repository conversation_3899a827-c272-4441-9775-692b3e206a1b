# s3-website-with-cloudfront - variables.tf
# ============================================

# Data Source
#-------------------------------------------
variable "account_name" {
  description = "Nome da conta. Certificar que nao existem espacos em branco entre as palavras."
  type        = string
}

variable "account_region" {
  description = "Regiao onde os recursos serao alocados."
  type        = string
}

# S3 Bucket
#-------------------------------------------
variable "business_unity" {
  description = "Nome da Unidade de Negocio. Este texto sera utilizado na construcao do nome do recurso."
  type        = string
}

variable "application_name" {
  description = "Nome da Aplicacao. Este texto sera utilizado na construcao do nome do recurso."
  type        = string
}

variable "environment_acronym" {
  description = "Acronimo do ambiente. Este texto sera utilizado na construcao do nome do recurso."
  type        = string
}

variable "bucket_suffix" {
  description = "Sufixo do Bucket. Este texto sera utilizado na construcao do nome do recurso."
  type        = string
}

variable "bucket_versioning" {
  description = "Define se o bucket S3 estara com o versionamento habilitado ou desabilitado."
  type        = string
  default     = "Enabled"
}

variable "bucket_expire_days" {
  description = "Quantidade em dias que sera utilizada na politica de expiracao de arquivos do bucket logs."
  type        = number
  default     = 30
}

variable "allowed_ip_ranges" {
  description = "Lista de ranges IP que sera utilizada na politica de acessos do Bucket."
  type        = list(any)
  default     = ["10.0.0.0/8", "**********/12", "***********/16"]
}

# CloudFront
#-------------------------------------------
variable "default_ttl" {
  description = "Tempo de vida padrao (em segundos) para o cache dos objetos no CloudFront."
  type        = number
  default     = 86400
}

variable "min_ttl" {
  description = "Tempo de vida minimo (em segundos) para o cache dos objetos no CloudFront."
  type        = number
  default     = 3600
}

variable "max_ttl" {
  description = "Tempo de vida maximo (em segundos) para o cache dos objetos no CloudFront."
  type        = number
  default     = 31536000
}

variable "allowed_locations" {
  description = "Lista de Geo localizacoes permitidas."
  type        = list(string)
  default     = ["BR"]
}

variable "acm_certificate_arn" {
  description = "ARN do certificado SSL do CloudFront. O certificado deve estar na regiao us-east-1."
  type        = string
}

# WAF
#-------------------------------------------
variable "rate_limit_limit" {
  description = "Limite de taxa para a regra de Rate Limit."
  type        = number
  default     = 100
}

variable "sql_injection_field" {
  description = "Campo para corresponder a regra de SQL Injection."
  type        = list(string)
  default     = ["all_query_arguments"]
}

variable "xss_field" {
  description = "Campo para corresponder a regra de XSS."
  type        = list(string)
  default     = ["all_query_arguments"]
}