# s3-website-with-cloudfront - cloudfront-distribution.tf
# ============================================

# CloudFront - Origin Access Control
#-------------------------------------------
resource "aws_cloudfront_origin_access_control" "this" {
  name                              = "CFront-S3-OAC-${var.business_unity}-${var.environment_acronym}"
  description                       = "CloudFront S3 OAC"
  origin_access_control_origin_type = "s3"
  signing_behavior                  = "always"
  signing_protocol                  = "sigv4"

  depends_on = [aws_s3_bucket.website]
}

# CloudFront - Distribution
#-------------------------------------------
resource "aws_cloudfront_distribution" "this" {
  dynamic "origin" {
    for_each = toset(var.application_name)
    content {
      domain_name              = aws_s3_bucket.website[origin.key].bucket_regional_domain_name
      origin_id                = "S3-Website-Origin-${origin.key}"
      origin_access_control_id = aws_cloudfront_origin_access_control.this.id
    }
  }

  enabled             = true
  is_ipv6_enabled     = true
  default_root_object = "index.html"
  # web_acl_id          = aws_wafv2_web_acl_association.this.arn # Linha comentada na imagem original

  viewer_certificate {
    acm_certificate_arn      = var.acm_certificate_arn
    ssl_support_method       = "sni-only"
    minimum_protocol_version = "TLSv1.2_2021"
  }

  restrictions {
    geo_restriction {
      restriction_type = "whitelist"
      locations        = var.allowed_locations
    }
  }

  custom_error_response {
    error_caching_min_ttl = 0
    error_code            = 403
    response_page_path    = "/error.html"
    response_code         = "200"
  }

  default_cache_behavior {
    allowed_methods        = ["GET", "HEAD"]
    cached_methods         = ["GET", "HEAD"]
    target_origin_id       = "S3-Website-Origin-${var.application_name[0]}"
    compress               = true
    
    forwarded_values {
      query_string = false
      cookies {
        forward = "none"
      }
      headers = ["Accept-Encoding"]
    }

    viewer_protocol_policy = "redirect-to-https"
    min_ttl                = var.min_ttl
    default_ttl            = var.default_ttl
    max_ttl                = var.max_ttl
  }

  dynamic "ordered_cache_behavior" {
    for_each = toset(var.application_name)
    content {
      path_pattern     = "/${ordered_cache_behavior.key}/*"
      allowed_methods  = ["GET", "HEAD"]
      cached_methods   = ["GET", "HEAD"]
      target_origin_id = "S3-Website-Origin-${ordered_cache_behavior.key}"
      compress         = true

      forwarded_values {
        query_string = false
        cookies {
          forward = "none"
        }
        headers = ["Accept-Encoding"]
      }
      
      viewer_protocol_policy = "redirect-to-https"
      min_ttl                = var.min_ttl
      default_ttl            = var.default_ttl
      max_ttl                = var.max_ttl
    }
  }

  logging_config {
    bucket          = aws_s3_bucket.cflogs.bucket_regional_domain_name
    include_cookies = false
    prefix          = "cloudfront-logs/"
  }

  ## Realtime log
  # realtime_log_config {
  #   name = "Real-Time log Configuration"
  #   sampling_rate = 100
  #   fields {
  #     name = "cs-uri-stem"
  #     value = "cs_uri_stem"
  #   }
  #   fields {
  #     name = "cs-uri-query"
  #     value = "cs_uri_query"
  #   }
  #   fields {
  #     name = "cs-method"
  #     value = "cs_method"
  #   }
  #   fields {
  #     name = "cs(Referer)"
  #     value = "cs_referer"
  #   }
  #   fields {
  #     name = "cs(User-Agent)"
  #     value = "cs_user_agent"
  #   }
  # }
  
  tags = {
    # TAGS técnicas
    T_name    = "${local.account_id}-cloudfront-${var.business_unity}-${var.environment_acronym}"
    T_service = "cloudfront"
  }

  depends_on = [aws_s3_bucket_ownership_controls.cflogs]
}