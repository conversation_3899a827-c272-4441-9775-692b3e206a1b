# s3-website-with-cloudfront - iam.tf
# ============================================

# IAM Policy - CloudFront access to the S3 Bucket do Website
#-------------------------------------------
resource "aws_iam_policy" "cf-website" {
  for_each    = toset(var.application_name)
  name        = "AccessToS3-${var.business_unity}-${each.value}-${var.environment_acronym}-${var.bucket_suffix}_cf"
  description = "IAM Policy to allow CloudFront to access S3 for application ${each.value}'s website files."

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect   = "Allow",
        Action   = "s3:GetObject",
        Resource = "${aws_s3_bucket.website[each.value].arn}/*"
      }
    ]
  })

  tags = {
    # TAGS tecnicas
    T_name    = "${aws_cloudfront_distribution.this.domain_name}"
    T_acao    = "s3"
    T_servico = "iam"
  }

  depends_on = [aws_s3_bucket.website]
}

resource "aws_iam_role" "cf-website" {
  for_each = toset(var.application_name)
  name     = "AccessToS3-${var.business_unity}-${each.value}-${var.environment_acronym}-${var.bucket_suffix}_cf"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action    = "sts:AssumeRole",
        Effect    = "Allow",
        Principal = {
          Service = "cloudfront.amazonaws.com"
        }
      }
    ]
  })

  depends_on = [aws_s3_bucket.website]
}

resource "aws_iam_role_policy_attachment" "cf-website" {
  for_each   = toset(var.application_name)
  policy_arn = aws_iam_policy.cf-website[each.value].arn
  role       = aws_iam_role.cf-website[each.value].name
}


# IAM Policy - CloudFront access to the S3 Bucket de Logs do CloudFront
#-------------------------------------------
resource "aws_iam_policy" "cf-logs" {
  name        = "AccessToS3-${var.business_unity}-cloudfront-${var.environment_acronym}-logs_cf"
  description = "IAM Policy to allow CloudFront to access S3 of ClodFront logs." # "ClodFront" é um typo da imagem original

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect   = "Allow",
        Action   = "s3:PutObject",
        Resource = "${aws_s3_bucket.cflogs.arn}/*"
      }
    ]
  })

  tags = {
    # TAGS tecnicas
    T_name    = "${aws_cloudfront_distribution.this.domain_name}"
    T_acao    = "s3"
    T_servico = "iam"
  }

  depends_on = [aws_s3_bucket.cflogs]
}

resource "aws_iam_role" "cf-logs" {
  name = "AccessToS3-${var.business_unity}-cloudfront-${var.environment_acronym}-logs_cf"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action    = "sts:AssumeRole",
        Effect    = "Allow",
        Principal = {
          Service = "cloudfront.amazonaws.com"
        }
      }
    ]
  })

  depends_on = [aws_s3_bucket.cflogs]
}

resource "aws_iam_role_policy_attachment" "cf-logs" {
  policy_arn = aws_iam_policy.cf-logs.arn
  role       = aws_iam_role.cf-logs.name
}


# IAM Policy - CloudFront access to the WebACL
#-------------------------------------------
resource "aws_iam_policy" "cf-waf" {
  name        = "AccessToWAF-${aws_cloudfront_distribution.this.id}_cf"
  description = "Policy to allow CloudFront to access WAF."

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "wafv2:GetWebACL",
          "wafv2:GetWebACLForResource",
          "wafv2:AssociateWebACL",
          "wafv2:DisassociateWebACL",
        ],
        Resource = aws_wafv2_web_acl.all_in_one.arn,
      },
    ]
  })

  tags = {
    # TAGS tecnicas
    T_name    = "${aws_cloudfront_distribution.this.domain_name}"
    T_acao    = "waf"
    T_servico = "iam"
  }
}

resource "aws_iam_role" "cf-waf" {
  name = "AccessToWAF-${aws_cloudfront_distribution.this.id}_cf"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action    = "sts:AssumeRole",
        Effect    = "Allow",
        Principal = {
          Service = "cloudfront.amazonaws.com"
        },
      },
    ]
  })
}

resource "aws_iam_role_policy_attachment" "cf-waf" {
  policy_arn = aws_iam_policy.cf-waf.arn
  role       = aws_iam_role.cf-waf.name
}


# IAM Policy - Read-Only for Dynatrace
#-------------------------------------------
resource "aws_iam_policy" "dynatrace" {
  name        = "AccessToCloudFront-${aws_cloudfront_distribution.this.id}_dyntrc"
  description = "IAM Policy to allow Dynatrace to access CloudFront."

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "cloudwatch:GetMetricStatistics",
          "cloudwatch:ListMetrics",
          "cloudfront:GetDistribution",
          "cloudfront:GetDistributionConfig",
          "cloudfront:ListDistributions"
        ],
        Resource = "*",
      },
    ]
  })

  tags = {
    # TAGS tecnicas
    T_name    = "${aws_cloudfront_distribution.this.domain_name}"
    T_acao    = "dynatrace"
    T_servico = "iam"
  }
}

resource "aws_iam_role" "dynatrace" {
  name = "AccessToCloudFront-${aws_cloudfront_distribution.this.id}_dyntrc"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action    = "sts:AssumeRole",
        Effect    = "Allow",
        Principal = {
          Service = "cloudwatch.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "dynatrace" {
  policy_arn = aws_iam_policy.dynatrace.arn
  role       = aws_iam_role.dynatrace.name
}