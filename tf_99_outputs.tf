# s3-website-with-cloudfront - outputs.tf
# ============================================

output "website_bucket_domain" {
  description = "Bucket domain name"
  value = {
    for key, policy in aws_s3_bucket.website :
    key => policy.bucket_regional_domain_name
  }
}

output "website_bucket_arn" {
  description = "Bucket ARN"
  value = {
    for key, policy in aws_s3_bucket.website :
    key => policy.arn
  }
}

output "website_bucket_name" {
  description = "Bucket name"
  value = {
    for key, policy in aws_s3_bucket.website :
    key => policy.bucket
  }
}

output "website_bucket_id" {
  description = "Bucket ID"
  value = {
    for key, policy in aws_s3_bucket.website :
    key => policy.id
  }
}

output "cflogs_bucket_domain" {
  description = "Bucket domain name"
  value       = aws_s3_bucket.cflogs.bucket_regional_domain_name
}

output "cflogs_bucket_arn" {
  description = "Bucket ARN"
  value       = aws_s3_bucket.cflogs.arn
}

output "cflogs_bucket_name" {
  description = "Bucket name"
  value       = aws_s3_bucket.cflogs.bucket
}

output "cflogs_bucket_id" {
  description = "Bucket ID"
  value       = aws_s3_bucket.cflogs.id
}

output "cloudfront_distribution_id" {
  description = "CloudFront Distribution ID"
  value       = aws_cloudfront_distribution.this.id
}

output "cloudfront_distribution_domain" {
  description = "CloudFront Distribution domain"
  value       = aws_cloudfront_distribution.this.domain_name
}

output "policy_map" {
  description = "Map with access policy ARNs ('action' : 'arn')"
  value = {
    "s3-cloudfront-website" = {
      for key, policy in aws_iam_policy.cf-website :
      key => policy.arn
    },
    "s3-cloudfront-cflogs" = aws_iam_policy.cf-logs.arn
    "waf-cloudfront"       = aws_iam_policy.cf-waf.arn
    "cloudfront-dynatrace" = aws_iam_policy.dynatrace.arn
  }
}

output "waf_acl_map" {
  description = "Map with WAF ACL ARNs ('acl' : 'arn')"
  value = {
    "geoblocking"   = aws_wafv2_web_acl.geoblocking.arn
    "rate_limit"    = aws_wafv2_web_acl.rate_limit.arn
    "sql_injection" = aws_wafv2_web_acl.sql_injection.arn
    "xss"           = aws_wafv2_web_acl.xss.arn
    "all_in_one"    = aws_wafv2_web_acl.all_in_one.arn
  }
}