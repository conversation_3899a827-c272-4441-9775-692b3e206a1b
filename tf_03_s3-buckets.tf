# s3-website-with-cloudfront-s3-buckets.tf
# ============================================

# S3 Bucket - Website Bucket
#-------------------------------------------
resource "aws_s3_bucket" "website" {
  for_each = toset(var.application_name)
  bucket   = "${local.account_id}-s3-${var.business_unity}-${each.value}-${var.environment_acronym}-${var.bucket_suffix}"

  tags = {
    # TAGS tecnicas
    T_name    = "${local.account_id}-s3-${var.business_unity}-${each.value}-${var.environment_acronym}-${var.bucket_suffix}"
    T_service = "s3"
  }
}

# Website Bucket - Public Access block
#-------------------------------------------
# resource "aws_s3_bucket_public_access_block" "website" {
#   for_each = toset(var.application_name)
#   bucket   = aws_s3_bucket.website[each.value].id

#   block_public_acls       = true
#   block_public_policy     = true
#   ignore_public_acls      = true
#   restrict_public_buckets = true
#   depends_on = [aws_s3_bucket.website[each.value]] # O depends_on explícito não é necessário aqui
# }

# Website Bucket - Website configuration
#-------------------------------------------
resource "aws_s3_bucket_website_configuration" "website" {
  for_each = toset(var.application_name)
  bucket   = aws_s3_bucket.website[each.value].id

  index_document {
    suffix = "index.html"
  }

  error_document {
    key = "error.html"
  }

  depends_on = [aws_s3_bucket.website]
}

# Website Bucket - Server Side Encryption
#-------------------------------------------
resource "aws_s3_bucket_server_side_encryption_configuration" "website" {
  for_each = toset(var.application_name)
  bucket   = aws_s3_bucket.website[each.value].id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }

  depends_on = [aws_s3_bucket.website]
}

# Website Bucket - Versioning
#-------------------------------------------
resource "aws_s3_bucket_versioning" "website" {
  for_each = toset(var.application_name)
  bucket   = aws_s3_bucket.website[each.value].id

  versioning_configuration {
    status = var.bucket_versioning
  }

  depends_on = [aws_s3_bucket.website]
}

# Website Bucket - Policy for CloudFront
#-------------------------------------------
resource "aws_s3_bucket_policy" "website" {
  for_each = toset(var.application_name)
  bucket   = aws_s3_bucket.website[each.value].id

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action    = "s3:GetObject",
        Effect    = "Allow",
        Principal = "*",
        Condition = {
          StringEquals = {
            "aws:SourceArn" = aws_cloudfront_distribution.this.arn
          }
        },
        Resource = "${aws_s3_bucket.website[each.value].arn}/*"
      }
    ]
  })

  depends_on = [aws_s3_bucket.website]
}


# CloudFront Logs Bucket - Bucket
#-------------------------------------------
resource "aws_s3_bucket" "cflogs" {
  bucket = "${local.account_id}-s3-${var.business_unity}-cloudfront-${var.environment_acronym}-logs"

  tags = {
    # TAGS tecnicas
    T_name    = "${local.account_id}-s3-${var.business_unity}-cloudfront-${var.environment_acronym}-logs"
    T_service = "s3"
  }
}

# CloudFront Logs Bucket - Policy
#-------------------------------------------
resource "aws_s3_bucket_policy" "cflogs" {
  bucket = aws_s3_bucket.cflogs.id

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action    = "s3:PutObject",
        Effect    = "Allow",
        Principal = "*",
        Condition = {
          StringEquals = {
            "aws:SourceArn" = aws_cloudfront_distribution.this.arn
          }
        },
        Resource = "${aws_s3_bucket.cflogs.arn}/*"
      },
      {
        Action    = "s3:GetObject",
        Effect    = "Allow",
        Principal = "*",
        Condition = {
          IpAddress = {
            "aws:SourceIp" = var.allowed_ip_ranges
          }
        },
        Resource = "${aws_s3_bucket.cflogs.arn}/*"
      },
      {
        Action    = "s3:GetObject",
        Effect    = "Allow",
        Principal = "*",
        Condition = {
          IpAddress = {
            "aws:SourceIp" = data.aws_vpc.main-vpc.cidr_block
          }
        },
        Resource = "${aws_s3_bucket.cflogs.arn}/*"
      }
    ]
  })

  depends_on = [aws_s3_bucket.cflogs]
}

# CloudFront Logs Bucket - Public Access block
#-------------------------------------------
# resource "aws_s3_bucket_public_access_block" "cflogs" {
#   bucket = aws_s3_bucket.cflogs.id

#   block_public_acls       = true
#   block_public_policy     = true
#   ignore_public_acls      = true
#   restrict_public_buckets = true

#   depends_on = [aws_s3_bucket.cflogs]
# }

# CloudFront Logs Bucket - Server Side Encryption
#-------------------------------------------
resource "aws_s3_bucket_server_side_encryption_configuration" "cflogs" {
  bucket = aws_s3_bucket.cflogs.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }

  depends_on = [aws_s3_bucket.cflogs]
}

# CloudFront Logs Bucket - Life Cycle
#-------------------------------------------
resource "aws_s3_bucket_lifecycle_configuration" "cflogs" {
  bucket = aws_s3_bucket.cflogs.id

  rule {
    id = "expire-logs"
    
    filter {}

    expiration {
      days = var.bucket_expire_days
    }
    
    status = "Enabled"
  }

  depends_on = [aws_s3_bucket.cflogs]
}

# CloudFront Logs Bucket - Ownership
#-------------------------------------------
resource "aws_s3_bucket_ownership_controls" "cflogs" {
  bucket = aws_s3_bucket.cflogs.id

  rule {
    object_ownership = "BucketOwnerPreferred"
  }

  depends_on = [aws_s3_bucket.cflogs]
}